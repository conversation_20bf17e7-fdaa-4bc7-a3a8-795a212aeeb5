import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Button } from 'antd';

/**
 * Demopage Portal - Entry page for Everest applications
 *
 * This component serves as a central hub for accessing all implemented applications
 * in the everest.base.ui/apps directory.
 */
const AppPortal: React.FC<any> = ({ pageState }) => {
  useEffect(() => {
    pageState.tabTitle = 'Test Application Portal 6';
  }, []);

  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // Application definitions
  const applications = [
    {
      id: 'testui',
      title: 'Data Explorer Test',
      description:
        'Advanced data exploration and management tool. Query, filter, and manipulate data from various models in the system.',
      icon: '🗄️',
      color: '#2e7d32',
      path: '/content/everest.base.ui/apps/testui/ui/TestUI',
      tags: ['Data', 'Admin', 'Explorer'],
    },
    {
      id: 'sflight',
      title: 'SAP SFLIGHT Demo',
      description:
        'Flight booking management system based on the SAP SFLIGHT demo. Manage flights, bookings, and customers in this interactive demo application.',
      icon: '✈️',
      color: '#1a3f73',
      path: '/content/everest.base.ui/apps/sflight/ui/SFlightApp',
      tags: ['Demo', 'Booking', 'Management'],
    },
    {
      id: 'prometheus',
      title: 'User Status Dashboard',
      description:
        'Monitor user activity and login events across the platform. View detailed user status information in both table and chart formats.',
      icon: '📊',
      color: '#c62828',
      path: '/content/everest.base.ui/apps/prometheus/ui/PrometheusApp',
      tags: ['Users', 'Monitoring', 'Analytics'],
    },
    {
      id: 'demopages',
      title: 'Demo Pages',
      description: 'Collection of demo pages showcasing various Everest functionalities with a convenient sidebar navigation.',
      icon: '📋',
      color: '#1976d2',
      path: '/content/everest.base.ui/apps/demopages/ui/DemopagePortal',
      tags: ['Demo', 'Examples', 'Development'],
    },
  ];

  // eslint-disable-next-line no-undef
  const handleCardClick = (path: string) => {
    // eslint-disable-next-line no-undef
    window.location.href = path;
  };

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        padding: '40px 20px',
      }}
    >
      <div
        style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '20px',
        }}
      >
        <header style={{ textAlign: 'center', marginBottom: '40px' }}>
          <Typography.Title
            level={1}
            style={{
              fontSize: '2.5rem',
              marginBottom: '16px',
              background: 'linear-gradient(90deg, #2c3e50, #4b6cb7)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            Test Application Portal
          </Typography.Title>
          <Typography.Paragraph
            style={{
              fontSize: '1.1rem',
              maxWidth: '700px',
              margin: '0 auto',
              color: '#5f6368',
            }}
          >
            Welcome! Select an application below to get started.
          </Typography.Paragraph>
        </header>

        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
            gap: '30px',
            marginBottom: '40px',
          }}
        >
          {applications.map((app) => (
            <Card
              key={app.id}
              style={{
                borderRadius: '12px',
                overflow: 'hidden',
                boxShadow:
                  hoveredCard === app.id
                    ? '0 15px 30px rgba(0,0,0,0.15)'
                    : '0 5px 15px rgba(0,0,0,0.08)',
                transition: 'all 0.3s ease',
                transform: hoveredCard === app.id ? 'translateY(-5px)' : 'none',
                cursor: 'pointer',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
              }}
              onClick={() => handleCardClick(app.path)}
              onMouseEnter={() => setHoveredCard(app.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              <div
                style={{
                  background: app.color,
                  padding: '30px 20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <span
                  style={{
                    fontSize: '4rem',
                    color: 'white',
                  }}
                >
                  {app.icon}
                </span>
              </div>
              <div
                style={{
                  padding: '20px',
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <Typography.Title
                  level={3}
                  style={{
                    marginBottom: '12px',
                    color: '#1a3f73',
                  }}
                >
                  {app.title}
                </Typography.Title>
                <Typography.Paragraph
                  style={{
                    marginBottom: '16px',
                    flex: 1,
                    color: '#5f6368',
                  }}
                >
                  {app.description}
                </Typography.Paragraph>
                <div
                  style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '16px',
                  }}
                >
                  {app.tags.map((tag) => (
                    <span
                      key={tag}
                      style={{
                        background: '#f0f2f5',
                        padding: '4px 10px',
                        borderRadius: '16px',
                        fontSize: '0.8rem',
                        color: '#5f6368',
                      }}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <Button
                  type="primary"
                  style={{
                    background: app.color,
                    borderColor: app.color,
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '8px',
                  }}
                >
                  <span>Launch Application</span>
                  <span>→</span>
                </Button>
              </div>
            </Card>
          ))}
        </div>

        <footer
          style={{
            textAlign: 'center',
            padding: '20px',
            borderTop: '1px solid #e0e0e0',
          }}
        >
          <Typography.Paragraph
            style={{
              fontSize: '0.9rem',
              color: '#8c8c8c',
            }}
          >
            &copy; {new Date().getFullYear()} Everest Platform • All
            applications are part of the Everest ecosystem
          </Typography.Paragraph>
        </footer>
      </div>
    </div>
  );
};

export default AppPortal;
